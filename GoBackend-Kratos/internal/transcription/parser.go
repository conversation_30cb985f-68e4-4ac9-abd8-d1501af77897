package transcription

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"gobackend-hvac-kratos/internal/ai"
	"gobackend-hvac-kratos/internal/customer"
)

// 📞 Transcription Parser - Parse phone call transcriptions from emails
type TranscriptionParser struct {
	log              *log.Helper
	gemma3           *ai.Gemma3Service
	customerService  *customer.CustomerIntelligenceService
	config           *TranscriptionConfig
}

// ⚙️ Transcription Configuration
type TranscriptionConfig struct {
	SupportedProviders []string `yaml:"supported_providers"` // whisper, google, azure, rev, otter
	DefaultLanguage    string   `yaml:"default_language"`    // en
	MinConfidence      float64  `yaml:"min_confidence"`      // 0.7
	MaxDuration        int      `yaml:"max_duration"`        // 3600 seconds (1 hour)
	EnableAIAnalysis   bool     `yaml:"enable_ai_analysis"`
}

// 📧 Transcription Email Structure
type TranscriptionEmail struct {
	// Call Metadata
	PhoneNumber   string        `json:"phone_number"`
	CallDirection string        `json:"call_direction"` // inbound, outbound
	CallDuration  time.Duration `json:"call_duration"`
	CallTimestamp time.Time     `json:"call_timestamp"`
	CallStatus    string        `json:"call_status"` // completed, missed, voicemail

	// Transcription Data
	Transcription string  `json:"transcription"`
	Confidence    float64 `json:"confidence"`
	Language      string  `json:"language"`
	Provider      string  `json:"provider"`

	// Caller Information
	CallerID     string `json:"caller_id,omitempty"`
	CallerName   string `json:"caller_name,omitempty"`
	CallerEmail  string `json:"caller_email,omitempty"`
	CallerCompany string `json:"caller_company,omitempty"`

	// Parsed Analysis
	CallPurpose    string   `json:"call_purpose,omitempty"`
	UrgencyLevel   string   `json:"urgency_level,omitempty"`
	ActionItems    []string `json:"action_items,omitempty"`
	FollowUpNeeded bool     `json:"follow_up_needed"`
	
	// Business Intelligence
	HVACRelevance      bool     `json:"hvac_relevance"`
	ServiceType        string   `json:"service_type,omitempty"`
	EquipmentMentioned []string `json:"equipment_mentioned,omitempty"`
	EstimatedValue     float64  `json:"estimated_value,omitempty"`

	// Source Information
	SourceEmailID string `json:"source_email_id"`
	SourceSubject string `json:"source_subject"`
}

// 🔍 Call Analysis Result
type CallAnalysis struct {
	// Core Analysis
	CallPurpose      string   `json:"call_purpose"`      // emergency, quote_request, follow_up, complaint, information
	UrgencyLevel     string   `json:"urgency_level"`     // critical, high, medium, low
	TechnicalIssues  []string `json:"technical_issues"`
	ServiceRequests  []string `json:"service_requests"`
	
	// Customer Intelligence
	CustomerMood     string   `json:"customer_mood"`     // frustrated, satisfied, neutral, angry, confused
	SatisfactionLevel string  `json:"satisfaction_level"` // very_satisfied, satisfied, neutral, dissatisfied, very_dissatisfied
	KnowledgeLevel   string   `json:"knowledge_level"`   // expert, intermediate, basic, novice
	
	// Business Value
	BusinessValue    string   `json:"business_value"`    // high, medium, low
	SalesOpportunity bool     `json:"sales_opportunity"`
	UpsellPotential  string   `json:"upsell_potential"`  // high, medium, low, none
	
	// Action Planning
	NextActions      []string   `json:"next_actions"`
	FollowUpDate     *time.Time `json:"follow_up_date,omitempty"`
	AssignedTo       string     `json:"assigned_to,omitempty"`
	Priority         int        `json:"priority"` // 1-10 scale
}

// 📱 Supported Transcription Providers
var SupportedProviders = map[string]*ProviderConfig{
	"whisper": {
		Name:           "OpenAI Whisper",
		EmailPatterns:  []string{"whisper", "openai"},
		PhoneRegex:     `(?i)phone[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)duration[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
	},
	"google": {
		Name:           "Google Speech-to-Text",
		EmailPatterns:  []string{"google", "speech", "cloud"},
		PhoneRegex:     `(?i)caller[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)length[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)accuracy[:\s]*(\d+\.?\d*)`,
	},
	"azure": {
		Name:           "Azure Speech Services",
		EmailPatterns:  []string{"azure", "microsoft", "cognitive"},
		PhoneRegex:     `(?i)number[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)time[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
	},
	"rev": {
		Name:           "Rev.ai",
		EmailPatterns:  []string{"rev.ai", "rev"},
		PhoneRegex:     `(?i)phone[:\s]*([+]?[\d\s\-\(\)]+)`,
		DurationRegex:  `(?i)duration[:\s]*(\d+)[:\s]*(\d+)`,
		ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
	},
}

// 🔧 Provider Configuration
type ProviderConfig struct {
	Name            string   `json:"name"`
	EmailPatterns   []string `json:"email_patterns"`
	PhoneRegex      string   `json:"phone_regex"`
	DurationRegex   string   `json:"duration_regex"`
	ConfidenceRegex string   `json:"confidence_regex"`
}

// NewTranscriptionParser creates a new transcription parser
func NewTranscriptionParser(
	gemma3 *ai.Gemma3Service,
	customerService *customer.CustomerIntelligenceService,
	config *TranscriptionConfig,
	logger log.Logger,
) *TranscriptionParser {
	return &TranscriptionParser{
		log:             log.NewHelper(logger),
		gemma3:          gemma3,
		customerService: customerService,
		config:          config,
	}
}

// 📧 Parse Transcription Email
func (p *TranscriptionParser) ParseTranscriptionEmail(
	ctx context.Context,
	emailContent string,
	emailSubject string,
	emailID string,
) (*TranscriptionEmail, error) {
	p.log.WithContext(ctx).Infof("Parsing transcription email: %s", emailSubject)

	transcription := &TranscriptionEmail{
		SourceEmailID: emailID,
		SourceSubject: emailSubject,
		Language:      p.config.DefaultLanguage,
	}

	// Detect provider
	provider := p.detectProvider(emailContent, emailSubject)
	transcription.Provider = provider

	// Extract phone number
	phoneNumber, err := p.extractPhoneNumber(emailContent, provider)
	if err != nil {
		p.log.WithContext(ctx).Warnf("Failed to extract phone number: %v", err)
	}
	transcription.PhoneNumber = phoneNumber

	// Extract call duration
	duration, err := p.extractCallDuration(emailContent, provider)
	if err != nil {
		p.log.WithContext(ctx).Warnf("Failed to extract call duration: %v", err)
	}
	transcription.CallDuration = duration

	// Extract transcription text
	transcriptionText := p.extractTranscriptionText(emailContent)
	transcription.Transcription = transcriptionText

	// Extract confidence score
	confidence := p.extractConfidence(emailContent, provider)
	transcription.Confidence = confidence

	// Extract call timestamp
	timestamp := p.extractCallTimestamp(emailContent, emailSubject)
	transcription.CallTimestamp = timestamp

	// Determine call direction
	direction := p.determineCallDirection(emailContent, emailSubject)
	transcription.CallDirection = direction

	// Extract caller information
	p.extractCallerInfo(emailContent, transcription)

	// Perform AI analysis if enabled
	if p.config.EnableAIAnalysis && transcriptionText != "" {
		analysis, err := p.analyzeCallContent(ctx, transcription)
		if err != nil {
			p.log.WithContext(ctx).Warnf("AI analysis failed: %v", err)
		} else {
			p.mapAnalysisToTranscription(analysis, transcription)
		}
	}

	p.log.WithContext(ctx).Infof("Transcription parsed successfully: %s (%s)", phoneNumber, provider)
	return transcription, nil
}

// 🔍 Detect Transcription Provider
func (p *TranscriptionParser) detectProvider(content, subject string) string {
	contentLower := strings.ToLower(content + " " + subject)

	for providerName, config := range SupportedProviders {
		for _, pattern := range config.EmailPatterns {
			if strings.Contains(contentLower, strings.ToLower(pattern)) {
				return providerName
			}
		}
	}

	return "unknown"
}

// 📞 Extract Phone Number
func (p *TranscriptionParser) extractPhoneNumber(content, provider string) (string, error) {
	config, exists := SupportedProviders[provider]
	if !exists {
		// Use generic phone regex
		config = &ProviderConfig{
			PhoneRegex: `(?i)(?:phone|number|caller)[:\s]*([+]?[\d\s\-\(\)]{10,})`,
		}
	}

	re := regexp.MustCompile(config.PhoneRegex)
	matches := re.FindStringSubmatch(content)

	if len(matches) > 1 {
		phone := strings.TrimSpace(matches[1])
		// Clean phone number
		phone = regexp.MustCompile(`[^\d+]`).ReplaceAllString(phone, "")
		return phone, nil
	}

	// Try generic phone patterns
	genericPatterns := []string{
		`(\+?1?[\s\-]?\(?[\d]{3}\)?[\s\-]?[\d]{3}[\s\-]?[\d]{4})`,
		`(\+?[\d]{1,3}[\s\-]?[\d]{3,4}[\s\-]?[\d]{3,4}[\s\-]?[\d]{3,4})`,
	}

	for _, pattern := range genericPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			phone := regexp.MustCompile(`[^\d+]`).ReplaceAllString(matches[1], "")
			if len(phone) >= 10 {
				return phone, nil
			}
		}
	}

	return "", fmt.Errorf("phone number not found")
}

// ⏱️ Extract Call Duration
func (p *TranscriptionParser) extractCallDuration(content, provider string) (time.Duration, error) {
	config, exists := SupportedProviders[provider]
	if !exists {
		config = &ProviderConfig{
			DurationRegex: `(?i)(?:duration|length|time)[:\s]*(\d+)[:\s]*(\d+)`,
		}
	}

	re := regexp.MustCompile(config.DurationRegex)
	matches := re.FindStringSubmatch(content)

	if len(matches) >= 3 {
		minutes, _ := strconv.Atoi(matches[1])
		seconds, _ := strconv.Atoi(matches[2])
		return time.Duration(minutes)*time.Minute + time.Duration(seconds)*time.Second, nil
	}

	// Try seconds only
	secondsRegex := regexp.MustCompile(`(?i)(?:duration|length)[:\s]*(\d+)[\s]*(?:seconds?|secs?)`)
	matches = secondsRegex.FindStringSubmatch(content)
	if len(matches) > 1 {
		seconds, _ := strconv.Atoi(matches[1])
		return time.Duration(seconds) * time.Second, nil
	}

	return 0, fmt.Errorf("call duration not found")
}

// 📝 Extract Transcription Text
func (p *TranscriptionParser) extractTranscriptionText(content string) string {
	// Common patterns for transcription text
	patterns := []string{
		`(?i)transcription[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
		`(?i)transcript[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
		`(?i)text[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
		`(?i)content[:\s]*\n(.*?)(?:\n\n|\n---|\nconfidence|$)`,
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			text := strings.TrimSpace(matches[1])
			if len(text) > 10 { // Minimum reasonable transcription length
				return text
			}
		}
	}

	// If no specific pattern found, try to extract the largest text block
	lines := strings.Split(content, "\n")
	var longestBlock string
	var currentBlock strings.Builder

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if len(line) > 20 && !strings.Contains(strings.ToLower(line), "phone") &&
			!strings.Contains(strings.ToLower(line), "duration") &&
			!strings.Contains(strings.ToLower(line), "confidence") {
			currentBlock.WriteString(line + " ")
		} else {
			if currentBlock.Len() > len(longestBlock) {
				longestBlock = currentBlock.String()
			}
			currentBlock.Reset()
		}
	}

	if currentBlock.Len() > len(longestBlock) {
		longestBlock = currentBlock.String()
	}

	return strings.TrimSpace(longestBlock)
}

// 📊 Extract Confidence Score
func (p *TranscriptionParser) extractConfidence(content, provider string) float64 {
	config, exists := SupportedProviders[provider]
	if !exists {
		config = &ProviderConfig{
			ConfidenceRegex: `(?i)confidence[:\s]*(\d+\.?\d*)`,
		}
	}

	re := regexp.MustCompile(config.ConfidenceRegex)
	matches := re.FindStringSubmatch(content)

	if len(matches) > 1 {
		confidence, err := strconv.ParseFloat(matches[1], 64)
		if err == nil {
			// Normalize to 0-1 range if it's in percentage
			if confidence > 1.0 {
				confidence = confidence / 100.0
			}
			return confidence
		}
	}

	return 0.8 // Default confidence
}

// 🕐 Extract Call Timestamp
func (p *TranscriptionParser) extractCallTimestamp(content, subject string) time.Time {
	// Try to extract timestamp from content or subject
	timePatterns := []string{
		`(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})`,
		`(\d{2}/\d{2}/\d{4}\s+\d{2}:\d{2})`,
		`(\d{2}-\d{2}-\d{4}\s+\d{2}:\d{2})`,
	}

	text := content + " " + subject
	for _, pattern := range timePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(text)
		if len(matches) > 1 {
			// Try different time formats
			formats := []string{
				"2006-01-02 15:04:05",
				"01/02/2006 15:04",
				"01-02-2006 15:04",
			}
			
			for _, format := range formats {
				if t, err := time.Parse(format, matches[1]); err == nil {
					return t
				}
			}
		}
	}

	// Default to current time if not found
	return time.Now()
}

// 🔄 Determine Call Direction
func (p *TranscriptionParser) determineCallDirection(content, subject string) string {
	text := strings.ToLower(content + " " + subject)

	inboundKeywords := []string{"incoming", "inbound", "received", "caller"}
	outboundKeywords := []string{"outgoing", "outbound", "made", "dialed"}

	for _, keyword := range inboundKeywords {
		if strings.Contains(text, keyword) {
			return "inbound"
		}
	}

	for _, keyword := range outboundKeywords {
		if strings.Contains(text, keyword) {
			return "outbound"
		}
	}

	return "inbound" // Default assumption
}

// 👤 Extract Caller Information
func (p *TranscriptionParser) extractCallerInfo(content string, transcription *TranscriptionEmail) {
	// Extract caller name
	namePatterns := []string{
		`(?i)caller[:\s]*([A-Za-z\s]+)`,
		`(?i)name[:\s]*([A-Za-z\s]+)`,
		`(?i)from[:\s]*([A-Za-z\s]+)`,
	}

	for _, pattern := range namePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(content)
		if len(matches) > 1 {
			name := strings.TrimSpace(matches[1])
			if len(name) > 2 && len(name) < 50 {
				transcription.CallerName = name
				break
			}
		}
	}

	// Extract caller email
	emailRegex := regexp.MustCompile(`([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})`)
	if matches := emailRegex.FindStringSubmatch(content); len(matches) > 0 {
		transcription.CallerEmail = matches[0]
	}

	// Extract company name from transcription text
	if transcription.Transcription != "" {
		companyPatterns := []string{
			`(?i)(?:from|at|with)\s+([A-Za-z\s&]+(?:Inc|LLC|Corp|Company|Co\.))`,
			`(?i)([A-Za-z\s&]+(?:Inc|LLC|Corp|Company|Co\.))`,
		}

		for _, pattern := range companyPatterns {
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(transcription.Transcription)
			if len(matches) > 1 {
				company := strings.TrimSpace(matches[1])
				if len(company) > 2 && len(company) < 100 {
					transcription.CallerCompany = company
					break
				}
			}
		}
	}
}

// 🧠 Analyze Call Content with Gemma 3
func (p *TranscriptionParser) analyzeCallContent(
	ctx context.Context,
	transcription *TranscriptionEmail,
) (*CallAnalysis, error) {
	// Prepare analysis request for Gemma 3
	hvacReq := &ai.HVACEmailAnalysisRequest{
		EmailContent: transcription.Transcription,
		Subject:      fmt.Sprintf("Phone Call Analysis - %s", transcription.PhoneNumber),
		AnalysisType: "phone_call",
		HVACContext: &ai.HVACContextData{
			ServiceType: "phone_support",
		},
	}

	// Get AI analysis
	response, err := p.gemma3.AnalyzeHVACEmail(ctx, hvacReq)
	if err != nil {
		return nil, fmt.Errorf("failed to analyze call content: %w", err)
	}

	// Map AI response to call analysis
	analysis := &CallAnalysis{
		CallPurpose:      "general",
		UrgencyLevel:     "medium",
		CustomerMood:     "neutral",
		SatisfactionLevel: "neutral",
		KnowledgeLevel:   "basic",
		BusinessValue:    "medium",
		Priority:         5,
	}

	// Map from Gemma 3 response
	if response.HVACRelevance != nil {
		analysis.CallPurpose = response.HVACRelevance.ServiceCategory
		analysis.UrgencyLevel = response.HVACRelevance.UrgencyLevel
	}

	if response.SentimentAnalysis != nil {
		analysis.CustomerMood = response.SentimentAnalysis.OverallSentiment
		analysis.SatisfactionLevel = response.SentimentAnalysis.CustomerSatisfaction
	}

	if response.TechnicalAnalysis != nil {
		analysis.TechnicalIssues = response.TechnicalAnalysis.DiagnosticClues
		analysis.KnowledgeLevel = response.TechnicalAnalysis.RequiredExpertise
	}

	if response.ActionPlan != nil {
		analysis.NextActions = response.ActionPlan.ImmediateActions
		if response.ActionPlan.FollowUpRequired {
			followUpTime := time.Now().Add(24 * time.Hour)
			analysis.FollowUpDate = &followUpTime
		}
	}

	if response.BusinessInsights != nil {
		analysis.BusinessValue = response.BusinessInsights.RevenueOpportunity
		analysis.SalesOpportunity = len(response.BusinessInsights.ServiceUpsell) > 0
		analysis.UpsellPotential = "medium"
		if analysis.SalesOpportunity {
			analysis.UpsellPotential = "high"
		}
	}

	// Calculate priority based on urgency and business value
	analysis.Priority = p.calculatePriority(analysis.UrgencyLevel, analysis.BusinessValue)

	return analysis, nil
}

// 🗺️ Map Analysis to Transcription
func (p *TranscriptionParser) mapAnalysisToTranscription(analysis *CallAnalysis, transcription *TranscriptionEmail) {
	transcription.CallPurpose = analysis.CallPurpose
	transcription.UrgencyLevel = analysis.UrgencyLevel
	transcription.ActionItems = analysis.NextActions
	transcription.FollowUpNeeded = analysis.FollowUpDate != nil

	// Set HVAC relevance
	hvacKeywords := []string{"hvac", "heating", "cooling", "air conditioning", "furnace", "ac", "heat pump"}
	transcriptionLower := strings.ToLower(transcription.Transcription)
	
	for _, keyword := range hvacKeywords {
		if strings.Contains(transcriptionLower, keyword) {
			transcription.HVACRelevance = true
			break
		}
	}

	// Extract service type and equipment
	if transcription.HVACRelevance {
		transcription.ServiceType = analysis.CallPurpose
		transcription.EquipmentMentioned = p.extractEquipmentMentions(transcription.Transcription)
	}

	// Estimate business value
	if analysis.BusinessValue == "high" {
		transcription.EstimatedValue = 1000.0
	} else if analysis.BusinessValue == "medium" {
		transcription.EstimatedValue = 500.0
	} else {
		transcription.EstimatedValue = 100.0
	}
}

// 🔧 Extract Equipment Mentions
func (p *TranscriptionParser) extractEquipmentMentions(transcription string) []string {
	equipment := []string{}
	transcriptionLower := strings.ToLower(transcription)

	equipmentKeywords := map[string][]string{
		"furnace":         {"furnace", "heater", "heating unit"},
		"air_conditioner": {"air conditioner", "ac unit", "cooling unit", "central air"},
		"heat_pump":       {"heat pump", "heatpump"},
		"thermostat":      {"thermostat", "temperature control"},
		"ductwork":        {"duct", "ductwork", "air duct", "ventilation"},
		"filter":          {"filter", "air filter"},
		"compressor":      {"compressor", "condenser"},
		"evaporator":      {"evaporator", "evap coil"},
	}

	for equipmentType, keywords := range equipmentKeywords {
		for _, keyword := range keywords {
			if strings.Contains(transcriptionLower, keyword) {
				equipment = append(equipment, equipmentType)
				break
			}
		}
	}

	return equipment
}

// 📊 Calculate Priority Score
func (p *TranscriptionParser) calculatePriority(urgency, businessValue string) int {
	urgencyScore := map[string]int{
		"critical": 10,
		"high":     8,
		"medium":   5,
		"low":      2,
	}

	businessScore := map[string]int{
		"high":   3,
		"medium": 2,
		"low":    1,
	}

	priority := urgencyScore[urgency] + businessScore[businessValue]
	if priority > 10 {
		priority = 10
	}
	if priority < 1 {
		priority = 1
	}

	return priority
}
