package octopus

import (
	"context"
	"fmt"
	"time"
)

// 👥 Build Customer Metrics
func (o *MorphicOctopusInterface) buildCustomerMetrics(ctx context.Context) (*CustomerMetrics, error) {
	var metrics CustomerMetrics
	
	// Total customers
	o.db.Model(&struct{ ID int64 }{}).Table("customers").Count(&metrics.TotalCustomers)
	
	// New customers today
	today := time.Now().Truncate(24 * time.Hour)
	o.db.Model(&struct{ ID int64 }{}).Table("customers").
		Where("created_at >= ?", today).Count(&metrics.NewToday)
	
	// New customers this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	o.db.Model(&struct{ ID int64 }{}).Table("customers").
		Where("created_at >= ?", weekStart).Count(&metrics.NewThisWeek)
	
	// Active customers (contacted in last 30 days)
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	o.db.Model(&struct{ ID int64 }{}).Table("customers").
		Where("last_contact_date >= ?", thirtyDaysAgo).Count(&metrics.ActiveCustomers)
	
	// High value customers (lifetime value > 5000)
	o.db.Model(&struct{ ID int64 }{}).Table("customer_analytics").
		Where("lifetime_value > ?", 5000).Count(&metrics.HighValueCustomers)
	
	// At risk customers (churn probability > 0.7)
	o.db.Model(&struct{ ID int64 }{}).Table("customer_analytics").
		Where("churn_probability > ?", 0.7).Count(&metrics.AtRiskCustomers)
	
	// Average satisfaction
	o.db.Model(&struct{ AvgSatisfaction float64 }{}).Table("customer_analytics").
		Select("AVG(avg_satisfaction)").Scan(&metrics.AvgSatisfaction)
	
	// Churn rate (placeholder calculation)
	metrics.ChurnRate = 0.05 // 5% placeholder
	
	// Average lifetime value
	o.db.Model(&struct{ AvgLTV float64 }{}).Table("customer_analytics").
		Select("AVG(lifetime_value)").Scan(&metrics.LifetimeValue)
	
	return &metrics, nil
}

// 📞 Build Transcription Statistics
func (o *MorphicOctopusInterface) buildTranscriptionStats(ctx context.Context) (*TranscriptionStats, error) {
	var stats TranscriptionStats
	
	// Total calls
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").Count(&stats.TotalCalls)
	
	// Calls today
	today := time.Now().Truncate(24 * time.Hour)
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("call_timestamp >= ?", today).Count(&stats.CallsToday)
	
	// Calls this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("call_timestamp >= ?", weekStart).Count(&stats.CallsThisWeek)
	
	// HVAC relevant calls
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("hvac_relevance = ?", true).Count(&stats.HVACRelevantCalls)
	
	// Emergency calls
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("urgency_level = ?", "critical").Count(&stats.EmergencyCalls)
	
	// Average confidence
	o.db.Model(&struct{ AvgConfidence float64 }{}).Table("transcription.call_transcriptions").
		Select("AVG(confidence_score)").Scan(&stats.AvgConfidence)
	
	// Processing backlog
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.transcription_emails").
		Where("processing_status = ?", "pending").Count(&stats.ProcessingBacklog)
	
	// Top caller companies
	var companies []string
	o.db.Model(&struct{ CallerCompany string }{}).Table("transcription.call_transcriptions").
		Select("caller_company").Where("caller_company IS NOT NULL AND caller_company != ''").
		Group("caller_company").Order("COUNT(*) DESC").Limit(5).Pluck("caller_company", &companies)
	stats.TopCallerCompanies = companies
	
	return &stats, nil
}

// 📧 Build Email Intelligence
func (o *MorphicOctopusInterface) buildEmailIntelligence(ctx context.Context) (*EmailIntelligence, error) {
	var intelligence EmailIntelligence
	
	// Total emails
	o.db.Model(&struct{ ID int64 }{}).Table("billionmail.email_messages").Count(&intelligence.TotalEmails)
	
	// Emails today
	today := time.Now().Truncate(24 * time.Hour)
	o.db.Model(&struct{ ID int64 }{}).Table("billionmail.email_messages").
		Where("created_at >= ?", today).Count(&intelligence.EmailsToday)
	
	// Emails this week
	weekStart := today.AddDate(0, 0, -int(today.Weekday()))
	o.db.Model(&struct{ ID int64 }{}).Table("billionmail.email_messages").
		Where("created_at >= ?", weekStart).Count(&intelligence.EmailsThisWeek)
	
	// HVAC relevant emails (from customer interactions)
	o.db.Model(&struct{ ID int64 }{}).Table("customer_interactions").
		Where("interaction_type = ? AND hvac_relevance = ?", "email", true).Count(&intelligence.HVACRelevantEmails)
	
	// Positive sentiment
	o.db.Model(&struct{ ID int64 }{}).Table("customer_interactions").
		Where("interaction_type = ? AND sentiment IN (?)", "email", []string{"positive", "very_positive"}).
		Count(&intelligence.PositiveSentiment)
	
	// Negative sentiment
	o.db.Model(&struct{ ID int64 }{}).Table("customer_interactions").
		Where("interaction_type = ? AND sentiment IN (?)", "email", []string{"negative", "very_negative"}).
		Count(&intelligence.NegativeSentiment)
	
	// Average processing time (placeholder)
	intelligence.AvgProcessingTime = 2 * time.Second
	
	// Top keywords (placeholder)
	intelligence.TopKeywords = []string{"HVAC", "repair", "maintenance", "emergency", "installation"}
	
	return &intelligence, nil
}

// 🤖 Build AI Performance
func (o *MorphicOctopusInterface) buildAIPerformance(ctx context.Context) (*AIPerformance, error) {
	var performance AIPerformance
	
	// Placeholder data - in real implementation, this would come from AI service metrics
	performance.TotalRequests = 10000
	performance.RequestsToday = 150
	performance.AvgResponseTime = 250 * time.Millisecond
	performance.SuccessRate = 97.5
	performance.ModelAccuracy = 92.3
	performance.TokensProcessed = 500000
	performance.ActiveModels = []string{"gemma:3b-instruct-q4_0", "bielik-v3", "nomic-embed-text"}
	performance.QueueLength = 5
	
	return &performance, nil
}

// 🚨 Build Realtime Alerts
func (o *MorphicOctopusInterface) buildRealtimeAlerts(ctx context.Context) []*Alert {
	alerts := []*Alert{}
	
	// Check for high churn risk customers
	var atRiskCount int
	o.db.Model(&struct{ ID int64 }{}).Table("customer_analytics").
		Where("churn_probability > ?", 0.8).Count(&atRiskCount)
	
	if atRiskCount > 0 {
		alerts = append(alerts, &Alert{
			ID:        "high_churn_risk",
			Type:      "warning",
			Title:     "High Churn Risk Customers",
			Message:   fmt.Sprintf("%d customers have high churn risk (>80%%)", atRiskCount),
			Source:    "customer_analytics",
			Timestamp: time.Now(),
			Actions:   []string{"view_customers", "send_retention_campaign"},
		})
	}
	
	// Check for processing backlog
	var backlogCount int
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.transcription_emails").
		Where("processing_status = ?", "pending").Count(&backlogCount)
	
	if backlogCount > 10 {
		alerts = append(alerts, &Alert{
			ID:        "transcription_backlog",
			Type:      "warning",
			Title:     "Transcription Processing Backlog",
			Message:   fmt.Sprintf("%d emails pending transcription processing", backlogCount),
			Source:    "transcription_service",
			Timestamp: time.Now(),
			Actions:   []string{"process_backlog", "check_service_health"},
		})
	}
	
	// Check for emergency calls
	var emergencyCount int
	o.db.Model(&struct{ ID int64 }{}).Table("transcription.call_transcriptions").
		Where("urgency_level = ? AND call_timestamp >= ?", "critical", time.Now().Add(-time.Hour)).
		Count(&emergencyCount)
	
	if emergencyCount > 0 {
		alerts = append(alerts, &Alert{
			ID:        "emergency_calls",
			Type:      "critical",
			Title:     "Emergency Calls Detected",
			Message:   fmt.Sprintf("%d emergency calls in the last hour", emergencyCount),
			Source:    "transcription_service",
			Timestamp: time.Now(),
			Actions:   []string{"view_emergency_calls", "notify_technicians"},
		})
	}
	
	return alerts
}

// ⚡ Build Quick Actions
func (o *MorphicOctopusInterface) buildQuickActions() []*QuickAction {
	return []*QuickAction{
		{
			ID:          "refresh_analytics",
			Title:       "Refresh Customer Analytics",
			Description: "Recalculate all customer analytics and insights",
			Icon:        "refresh",
			Endpoint:    "/api/customers/analytics/refresh",
			Method:      "POST",
			Category:    "analytics",
		},
		{
			ID:          "process_transcriptions",
			Title:       "Process Pending Transcriptions",
			Description: "Process all pending transcription emails",
			Icon:        "play_arrow",
			Endpoint:    "/api/transcription/process",
			Method:      "POST",
			Category:    "transcription",
		},
		{
			ID:          "send_maintenance_reminders",
			Title:       "Send Maintenance Reminders",
			Description: "Send automated maintenance reminders to customers",
			Icon:        "email",
			Endpoint:    "/api/email/campaigns",
			Method:      "POST",
			Category:    "email",
		},
		{
			ID:          "backup_database",
			Title:       "Backup Database",
			Description: "Create a full database backup",
			Icon:        "backup",
			Endpoint:    "/api/system/backup",
			Method:      "POST",
			Category:    "system",
		},
		{
			ID:          "restart_ai_service",
			Title:       "Restart AI Service",
			Description: "Restart the AI analysis service",
			Icon:        "restart_alt",
			Endpoint:    "/api/services/ai/restart",
			Method:      "POST",
			Category:    "ai",
			Dangerous:   true,
		},
		{
			ID:          "export_customer_data",
			Title:       "Export Customer Data",
			Description: "Export customer data for analysis",
			Icon:        "download",
			Endpoint:    "/api/customers/export",
			Method:      "POST",
			Category:    "data",
		},
	}
}

// 🎨 Generate Dashboard HTML
func (o *MorphicOctopusInterface) generateDashboardHTML() string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐙 Morphic Octopus Interface - HVAC Backend Management</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            padding: 20px; 
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #4a5568;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child {
            border-bottom: none;
        }
        .metric-value {
            font-weight: bold;
            color: #2d3748;
        }
        .status-healthy { color: #38a169; }
        .status-warning { color: #d69e2e; }
        .status-error { color: #e53e3e; }
        .alert {
            background: #fed7d7;
            border: 1px solid #feb2b2;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
        }
        .alert.warning {
            background: #fefcbf;
            border-color: #f6e05e;
        }
        .alert.critical {
            background: #fed7d7;
            border-color: #fc8181;
        }
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .action-btn {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: block;
        }
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(66, 153, 225, 0.4);
        }
        .action-btn.dangerous {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: white;
        }
        .spinner {
            border: 4px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .ws-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .ws-connected { background: rgba(56, 161, 105, 0.9); }
        .ws-disconnected { background: rgba(229, 62, 62, 0.9); }
    </style>
</head>
<body>
    <div class="ws-status" id="wsStatus">🔌 Connecting...</div>
    
    <div class="container">
        <div class="header">
            <h1>🐙 Morphic Octopus Interface</h1>
            <p>Ultimate HVAC Backend Management Dashboard</p>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Loading dashboard data...</p>
        </div>
        
        <div id="dashboard" style="display: none;">
            <div class="dashboard-grid">
                <div class="card">
                    <h3>🔧 System Status</h3>
                    <div id="systemStatus"></div>
                </div>
                
                <div class="card">
                    <h3>🏥 Service Health</h3>
                    <div id="serviceHealth"></div>
                </div>
                
                <div class="card">
                    <h3>👥 Customer Metrics</h3>
                    <div id="customerMetrics"></div>
                </div>
                
                <div class="card">
                    <h3>📞 Transcription Stats</h3>
                    <div id="transcriptionStats"></div>
                </div>
                
                <div class="card">
                    <h3>📧 Email Intelligence</h3>
                    <div id="emailIntelligence"></div>
                </div>
                
                <div class="card">
                    <h3>🤖 AI Performance</h3>
                    <div id="aiPerformance"></div>
                </div>
            </div>
            
            <div class="card">
                <h3>🚨 Real-time Alerts</h3>
                <div id="alerts"></div>
            </div>
            
            <div class="card">
                <h3>⚡ Quick Actions</h3>
                <div class="quick-actions" id="quickActions"></div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let reconnectInterval = null;

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = protocol + '//' + window.location.host + '/api/dashboard/ws';
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                document.getElementById('wsStatus').textContent = '🔌 Connected';
                document.getElementById('wsStatus').className = 'ws-status ws-connected';
                if (reconnectInterval) {
                    clearInterval(reconnectInterval);
                    reconnectInterval = null;
                }
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateDashboard(data);
            };
            
            ws.onclose = function() {
                document.getElementById('wsStatus').textContent = '🔌 Disconnected';
                document.getElementById('wsStatus').className = 'ws-status ws-disconnected';
                
                if (!reconnectInterval) {
                    reconnectInterval = setInterval(connectWebSocket, 5000);
                }
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function loadDashboard() {
            fetch('/api/dashboard/data')
                .then(response => response.json())
                .then(data => {
                    updateDashboard(data);
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('dashboard').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error loading dashboard:', error);
                    document.getElementById('loading').innerHTML = '<p>❌ Failed to load dashboard data</p>';
                });
        }

        function updateDashboard(data) {
            // Update system status
            if (data.system_status) {
                const systemHtml = Object.entries(data.system_status).map(([key, value]) => {
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    let displayValue = value;
                    if (typeof value === 'number' && key.includes('usage')) {
                        displayValue = value.toFixed(1) + '%';
                    }
                    return '<div class="metric"><span>' + displayKey + '</span><span class="metric-value">' + displayValue + '</span></div>';
                }).join('');
                document.getElementById('systemStatus').innerHTML = systemHtml;
            }

            // Update service health
            if (data.service_health) {
                const serviceHtml = Object.entries(data.service_health).map(([key, service]) => {
                    const statusClass = service.status === 'healthy' ? 'status-healthy' : 
                                      service.status === 'degraded' ? 'status-warning' : 'status-error';
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    return '<div class="metric"><span>' + displayKey + '</span><span class="metric-value ' + statusClass + '">' + service.status + '</span></div>';
                }).join('');
                document.getElementById('serviceHealth').innerHTML = serviceHtml;
            }

            // Update customer metrics
            if (data.customer_metrics) {
                const customerHtml = Object.entries(data.customer_metrics).map(([key, value]) => {
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    let displayValue = value;
                    if (typeof value === 'number' && (key.includes('rate') || key.includes('satisfaction'))) {
                        displayValue = value.toFixed(2);
                    }
                    return '<div class="metric"><span>' + displayKey + '</span><span class="metric-value">' + displayValue + '</span></div>';
                }).join('');
                document.getElementById('customerMetrics').innerHTML = customerHtml;
            }

            // Update transcription stats
            if (data.transcription_stats) {
                const transcriptionHtml = Object.entries(data.transcription_stats).map(([key, value]) => {
                    if (key === 'top_caller_companies') return '';
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    let displayValue = value;
                    if (typeof value === 'number' && key.includes('confidence')) {
                        displayValue = (value * 100).toFixed(1) + '%';
                    }
                    return '<div class="metric"><span>' + displayKey + '</span><span class="metric-value">' + displayValue + '</span></div>';
                }).join('');
                document.getElementById('transcriptionStats').innerHTML = transcriptionHtml;
            }

            // Update email intelligence
            if (data.email_intelligence) {
                const emailHtml = Object.entries(data.email_intelligence).map(([key, value]) => {
                    if (key === 'top_keywords') return '';
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    return '<div class="metric"><span>' + displayKey + '</span><span class="metric-value">' + value + '</span></div>';
                }).join('');
                document.getElementById('emailIntelligence').innerHTML = emailHtml;
            }

            // Update AI performance
            if (data.ai_performance) {
                const aiHtml = Object.entries(data.ai_performance).map(([key, value]) => {
                    if (key === 'active_models') return '';
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    let displayValue = value;
                    if (typeof value === 'number' && (key.includes('rate') || key.includes('accuracy'))) {
                        displayValue = value.toFixed(1) + '%';
                    }
                    return '<div class="metric"><span>' + displayKey + '</span><span class="metric-value">' + displayValue + '</span></div>';
                }).join('');
                document.getElementById('aiPerformance').innerHTML = aiHtml;
            }

            // Update alerts
            if (data.realtime_alerts) {
                const alertsHtml = data.realtime_alerts.map(alert => {
                    return '<div class="alert ' + alert.type + '"><strong>' + alert.title + '</strong><br>' + alert.message + '</div>';
                }).join('');
                document.getElementById('alerts').innerHTML = alertsHtml || '<p>No active alerts</p>';
            }

            // Update quick actions
            if (data.quick_actions) {
                const actionsHtml = data.quick_actions.map(action => {
                    const dangerousClass = action.dangerous ? ' dangerous' : '';
                    return '<button class="action-btn' + dangerousClass + '" onclick="executeAction(\'' + action.endpoint + '\', \'' + action.method + '\')">' + action.title + '</button>';
                }).join('');
                document.getElementById('quickActions').innerHTML = actionsHtml;
            }
        }

        function executeAction(endpoint, method) {
            if (confirm('Are you sure you want to execute this action?')) {
                fetch(endpoint, { method: method })
                    .then(response => response.json())
                    .then(data => {
                        alert('Action executed successfully!');
                        loadDashboard(); // Refresh dashboard
                    })
                    .catch(error => {
                        alert('Action failed: ' + error.message);
                    });
            }
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            connectWebSocket();
            
            // Refresh dashboard every 30 seconds as fallback
            setInterval(loadDashboard, 30000);
        });
    </script>
</body>
</html>`
}