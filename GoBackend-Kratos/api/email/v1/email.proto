syntax = "proto3";

package api.email.v1;

option go_package = "gobackend-hvac-kratos/api/email/v1;v1";

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";

// Email Service Definition
service EmailService {
  rpc SendEmail(SendEmailRequest) returns (SendEmailResponse) {
    option (google.api.http) = {
      post: "/api/v1/emails/send"
      body: "*"
    };
  }
  
  rpc ListEmails(ListEmailsRequest) returns (ListEmailsResponse) {
    option (google.api.http) = {
      get: "/api/v1/emails"
    };
  }
  
  rpc CreateCampaign(CreateCampaignRequest) returns (CreateCampaignResponse) {
    option (google.api.http) = {
      post: "/api/v1/campaigns"
      body: "*"
    };
  }
  
  rpc GetCampaignStats(GetCampaignStatsRequest) returns (GetCampaignStatsResponse) {
    option (google.api.http) = {
      get: "/api/v1/campaigns/{campaign_id}/stats"
    };
  }
  
  rpc AnalyzeSentiment(AnalyzeSentimentRequest) returns (AnalyzeSentimentResponse) {
    option (google.api.http) = {
      post: "/api/v1/emails/analyze-sentiment"
      body: "*"
    };
  }
}

// Message Definitions
message EmailMessage {
  string id = 1;
  string from = 2;
  repeated string to = 3;
  repeated string cc = 4;
  repeated string bcc = 5;
  string subject = 6;
  string body = 7;
  string html_body = 8;
  repeated Attachment attachments = 9;
  map&lt;string, string&gt; headers = 10;
  string priority = 11;
  google.protobuf.Timestamp timestamp = 12;
  string status = 13;
}message Attachment {
  string filename = 1;
  string content_type = 2;
  int64 size = 3;
  bytes data = 4;
  string url = 5;
}

message Campaign {
  string id = 1;
  string name = 2;
  string subject = 3;
  string template = 4;
  repeated string recipients = 5;
  google.protobuf.Timestamp scheduled_at = 6;
  string status = 7;
  CampaignStats stats = 8;
}

message CampaignStats {
  int32 sent = 1;
  int32 delivered = 2;
  int32 opened = 3;
  int32 clicked = 4;
  int32 bounced = 5;
  double open_rate = 6;
  double click_rate = 7;
}

// Request/Response Messages
message SendEmailRequest {
  string from = 1;
  repeated string to = 2;
  repeated string cc = 3;
  repeated string bcc = 4;
  string subject = 5;
  string body = 6;
  string html_body = 7;
  repeated Attachment attachments = 8;
  string priority = 9;
}

message SendEmailResponse {
  string email_id = 1;
  string status = 2;
  string message = 3;
}

message ListEmailsRequest {
  int32 limit = 1;
  int32 offset = 2;
  string status = 3;
}

message ListEmailsResponse {
  repeated EmailMessage emails = 1;
  int32 total = 2;
}message CreateCampaignRequest {
  string name = 1;
  string subject = 2;
  string template = 3;
  repeated string recipients = 4;
  google.protobuf.Timestamp scheduled_at = 5;
}

message CreateCampaignResponse {
  Campaign campaign = 1;
}

message GetCampaignStatsRequest {
  string campaign_id = 1;
}

message GetCampaignStatsResponse {
  CampaignStats stats = 1;
}

message AnalyzeSentimentRequest {
  string content = 1;
}

message AnalyzeSentimentResponse {
  string sentiment = 1;
  double confidence = 2;
}